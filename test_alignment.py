#!/usr/bin/env python3
"""
Script test để kiểm tra thuật toán căn chỉnh ảnh OMR mới
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.omr_debug_processor import OMRDebugProcessor
import cv2
import logging

# Cấu hình logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_alignment():
    """Test thuật toán căn chỉnh với ảnh mẫu"""

    # Khởi tạo processor
    processor = OMRDebugProcessor()

    # Đường dẫn ảnh test
    test_image_path = "data/grading/2.jpg"

    if not os.path.exists(test_image_path):
        print(f"❌ Không tìm thấy ảnh test: {test_image_path}")
        return

    print(f"🔍 Testing alignment với ảnh: {test_image_path}")

    try:
        # Xử lý ảnh
        result = processor.process_image_debug(test_image_path)

        print("✅ Xử lý hoàn tất!")
        print(f"📊 Kết quả:")
        print(f"   - Student ID: {result.get('student_id', 'N/A')}")
        print(f"   - Test Code: {result.get('test_code', 'N/A')}")
        print(f"   - Answers: {len(result.get('answers', {}))} câu")

        # Kiểm tra ảnh debug
        debug_dir = "data/grading/debug"
        if os.path.exists(debug_dir):
            debug_files = [f for f in os.listdir(debug_dir) if f.endswith(".jpg")]
            print(f"📷 Đã tạo {len(debug_files)} ảnh debug trong {debug_dir}/")

            # Liệt kê các ảnh quan trọng
            important_files = [
                "01_original.jpg",
                "03_corners_detected.jpg",
                "03b_corners_ordered.jpg",
                "04_aligned.jpg",
            ]

            for file in important_files:
                if file in debug_files:
                    print(f"   ✅ {file}")
                else:
                    print(f"   ❌ {file} - MISSING")

    except Exception as e:
        print(f"❌ Lỗi xử lý: {e}")
        import traceback

        traceback.print_exc()


def compare_before_after():
    """So sánh ảnh trước và sau căn chỉnh"""

    debug_dir = "data/grading/debug"
    original_path = os.path.join(debug_dir, "01_original.jpg")
    aligned_path = os.path.join(debug_dir, "04_aligned.jpg")
    corners_path = os.path.join(debug_dir, "03b_corners_ordered.jpg")

    if all(os.path.exists(p) for p in [original_path, aligned_path, corners_path]):
        print("\n📊 So sánh kết quả:")

        # Đọc ảnh
        original = cv2.imread(original_path)
        aligned = cv2.imread(aligned_path)
        corners = cv2.imread(corners_path)

        print(f"   - Ảnh gốc: {original.shape}")
        print(f"   - Ảnh căn chỉnh: {aligned.shape}")

        # Kiểm tra xem ảnh căn chỉnh có đúng kích thước không
        expected_size = (1536, 1086)  # height, width
        if aligned.shape[:2] == expected_size:
            print("   ✅ Kích thước ảnh căn chỉnh ĐÚNG")
        else:
            print(
                f"   ❌ Kích thước ảnh căn chỉnh SAI - Expected: {expected_size}, Got: {aligned.shape[:2]}"
            )

        # Tạo ảnh so sánh
        comparison_path = os.path.join(debug_dir, "comparison.jpg")

        # Resize ảnh gốc để so sánh
        original_resized = cv2.resize(original, (aligned.shape[1], aligned.shape[0]))

        # Ghép ảnh ngang
        comparison = cv2.hconcat([original_resized, aligned])

        # Thêm text
        cv2.putText(
            comparison,
            "ORIGINAL",
            (50, 50),
            cv2.FONT_HERSHEY_SIMPLEX,
            2,
            (0, 0, 255),
            3,
        )
        cv2.putText(
            comparison,
            "ALIGNED",
            (aligned.shape[1] + 50, 50),
            cv2.FONT_HERSHEY_SIMPLEX,
            2,
            (0, 255, 0),
            3,
        )

        cv2.imwrite(comparison_path, comparison)
        print(f"   📷 Đã tạo ảnh so sánh: {comparison_path}")

    else:
        print("❌ Không tìm thấy đủ ảnh debug để so sánh")


if __name__ == "__main__":
    print("TESTING OMR ALIGNMENT ALGORITHM")
    print("=" * 50)

    test_alignment()
    compare_before_after()

    print("\nHuong dan kiem tra:")
    print("1. Mo thu muc data/grading/debug/")
    print("2. Xem anh 03b_corners_ordered.jpg - kiem tra thu tu goc TL-TR-BR-BL")
    print("3. Xem anh 04_aligned.jpg - kiem tra anh da can chinh")
    print("4. Xem anh comparison.jpg - so sanh truoc/sau")
    print("5. Neu anh can chinh van sai, can dieu chinh thuat toan them")
