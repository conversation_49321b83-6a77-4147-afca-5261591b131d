#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import numpy as np
import os


def compare_alignment():
    """So sánh ảnh trước và sau căn chỉnh"""

    debug_dir = "data/grading/debug"
    original_path = "data/grading/2.jpg"
    aligned_path = os.path.join(debug_dir, "03_aligned.jpg")
    corners_path = os.path.join(debug_dir, "03b_corners_ordered.jpg")

    if not all(os.path.exists(p) for p in [original_path, aligned_path, corners_path]):
        print("Khong tim thay du anh de so sanh")
        return

    # Đọc ảnh
    original = cv2.imread(original_path)
    aligned = cv2.imread(aligned_path)
    corners = cv2.imread(corners_path)

    print(f"Anh goc: {original.shape}")
    print(f"Anh can chinh: {aligned.shape}")

    # <PERSON><PERSON><PERSON> tra kích thước
    expected_size = (1536, 1086)  # height, width
    if aligned.shape[:2] == expected_size:
        print("OK Kich thuoc anh can chinh DUNG")
    else:
        print(
            f"ERROR Kich thuoc anh can chinh SAI - Expected: {expected_size}, Got: {aligned.shape[:2]}"
        )

    # Tạo ảnh so sánh
    comparison_dir = os.path.join(debug_dir, "comparison")
    os.makedirs(comparison_dir, exist_ok=True)

    # 1. So sánh kích thước gốc
    original_resized = cv2.resize(original, (aligned.shape[1], aligned.shape[0]))
    comparison1 = cv2.hconcat([original_resized, aligned])

    # Thêm text
    cv2.putText(
        comparison1, "ORIGINAL", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 255), 3
    )
    cv2.putText(
        comparison1,
        "ALIGNED",
        (aligned.shape[1] + 50, 50),
        cv2.FONT_HERSHEY_SIMPLEX,
        2,
        (0, 255, 0),
        3,
    )

    comparison1_path = os.path.join(comparison_dir, "before_after.jpg")
    cv2.imwrite(comparison1_path, comparison1)
    print(f"OK Da tao anh so sanh: {comparison1_path}")

    # 2. Hiển thị corners detected
    corners_resized = cv2.resize(corners, (600, 400))
    corners_path_out = os.path.join(comparison_dir, "corners_detected.jpg")
    cv2.imwrite(corners_path_out, corners_resized)
    print(f"OK Da tao anh corners: {corners_path_out}")

    # 3. Tạo grid overlay trên ảnh aligned để kiểm tra căn chỉnh
    aligned_with_grid = aligned.copy()
    h, w = aligned.shape[:2]

    # Vẽ grid
    grid_color = (0, 255, 255)  # Yellow
    grid_thickness = 1

    # Vertical lines
    for i in range(0, w, w // 10):
        cv2.line(aligned_with_grid, (i, 0), (i, h), grid_color, grid_thickness)

    # Horizontal lines
    for i in range(0, h, h // 10):
        cv2.line(aligned_with_grid, (0, i), (w, i), grid_color, grid_thickness)

    # Vẽ ROI regions theo tọa độ từ code
    # Student ID region
    sid_x1, sid_y1 = int(1300 / 1807 * w), int(0 / 2555 * h)
    sid_x2, sid_y2 = int(1590 / 1807 * w), int(1012 / 2555 * h)
    cv2.rectangle(aligned_with_grid, (sid_x1, sid_y1), (sid_x2, sid_y2), (0, 255, 0), 2)
    cv2.putText(
        aligned_with_grid,
        "Student ID",
        (sid_x1, sid_y1 - 10),
        cv2.FONT_HERSHEY_SIMPLEX,
        0.7,
        (0, 255, 0),
        2,
    )

    # Test Code region
    tc_x1, tc_y1 = int(1418 / 1807 * w), int(254 / 2555 * h)
    tc_x2, tc_y2 = int(1726 / 1807 * w), int(821 / 2555 * h)
    cv2.rectangle(aligned_with_grid, (tc_x1, tc_y1), (tc_x2, tc_y2), (255, 0, 0), 2)
    cv2.putText(
        aligned_with_grid,
        "Test Code",
        (tc_x1, tc_y1 - 10),
        cv2.FONT_HERSHEY_SIMPLEX,
        0.7,
        (255, 0, 0),
        2,
    )

    grid_path = os.path.join(comparison_dir, "aligned_with_grid.jpg")
    cv2.imwrite(grid_path, aligned_with_grid)
    print(f"OK Da tao anh grid: {grid_path}")

    print(f"\nKiem tra ket qua trong thu muc: {comparison_dir}/")
    print("1. before_after.jpg - So sanh truoc/sau")
    print("2. corners_detected.jpg - Cac goc da phat hien")
    print("3. aligned_with_grid.jpg - Anh can chinh voi grid va ROI")


if __name__ == "__main__":
    print("SO SANH KET QUA CAN CHINH ANH")
    print("=" * 40)
    compare_alignment()
