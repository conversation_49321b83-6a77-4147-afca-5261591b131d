#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.omr_debug_processor import OMRDebugProcessor
import cv2
import logging

# Cấu hình logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_alignment():
    """Test thuật toán căn chỉnh với ảnh mẫu"""

    # Khởi tạo processor
    processor = OMRDebugProcessor()

    # Đường dẫn ảnh test
    test_image_path = "data/grading/2.jpg"

    if not os.path.exists(test_image_path):
        print(f"Khong tim thay anh test: {test_image_path}")
        return

    print(f"Testing alignment voi anh: {test_image_path}")

    try:
        # Xử lý ảnh
        result = processor.process_image_file(test_image_path)

        print("Xu ly hoan tat!")
        print(f"Ket qua:")
        print(f"   - Student ID: {result.get('student_id', 'N/A')}")
        print(f"   - Test Code: {result.get('test_code', 'N/A')}")
        print(f"   - Answers: {len(result.get('answers', {}))} cau")

        # Kiểm tra ảnh debug
        debug_dir = "data/grading/debug"
        if os.path.exists(debug_dir):
            debug_files = [f for f in os.listdir(debug_dir) if f.endswith(".jpg")]
            print(f"Da tao {len(debug_files)} anh debug trong {debug_dir}/")

            # Liệt kê các ảnh quan trọng
            important_files = [
                "01_original.jpg",
                "03_corners_detected.jpg",
                "03b_corners_ordered.jpg",
                "04_aligned.jpg",
            ]

            for file in important_files:
                if file in debug_files:
                    print(f"   OK {file}")
                else:
                    print(f"   MISSING {file}")

    except Exception as e:
        print(f"Loi xu ly: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    print("TESTING OMR ALIGNMENT ALGORITHM")
    print("=" * 50)

    test_alignment()

    print("\nHuong dan kiem tra:")
    print("1. Mo thu muc data/grading/debug/")
    print("2. Xem anh 03b_corners_ordered.jpg - kiem tra thu tu goc TL-TR-BR-BL")
    print("3. Xem anh 04_aligned.jpg - kiem tra anh da can chinh")
